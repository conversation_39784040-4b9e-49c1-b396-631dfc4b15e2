import CryptoJS from 'crypto-js'

function WXBizDataCrypt(appId, sessionKey) {
  this.appId = appId
  this.sessionKey = sessionKey
}

WXBizDataCrypt.prototype.decryptData = function (encryptedData, iv) {
  try {
    // 验证输入参数
    if (!encryptedData || !iv || !this.sessionKey) {
      throw new Error('Missing required parameters: encryptedData, iv, or sessionKey')
    }

    console.log('Decryption inputs:', {
      sessionKey: this.sessionKey,
      encryptedData: encryptedData.substring(0, 20) + '...',
      iv: iv,
      appId: this.appId
    })

    // 使用 crypto-js 进行解密
    console.log('Original sessionKey:', this.sessionKey)
    console.log('Original IV:', iv)

    // 解密 - 使用正确的 WeChat 解密方式
    // sessionKey 需要是 32 字节，iv 需要是 16 字节
    var key = CryptoJS.enc.Base64.parse(this.sessionKey)
    var iv = CryptoJS.enc.Base64.parse(iv)

    console.log('Key size (words):', key.words.length, 'bytes:', key.words.length * 4)
    console.log('IV size (words):', iv.words.length, 'bytes:', iv.words.length * 4)

    // 直接使用 base64 字符串进行解密
    var decrypted = CryptoJS.AES.decrypt(
      encryptedData,
      key,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )

    console.log('Decrypted object:', decrypted)

    // 转换为 UTF-8 字符串
    var decryptedString = decrypted.toString(CryptoJS.enc.Utf8)
    console.log('Decrypted string:', decryptedString)

    if (!decryptedString) {
      throw new Error('Decryption failed - empty result')
    }

    var decoded = JSON.parse(decryptedString)
    console.log('Decoded JSON:', decoded)

    // 验证 watermark
    if (!decoded.watermark || !decoded.watermark.appid) {
      throw new Error('Invalid decrypted data - missing watermark')
    }

    if (decoded.watermark.appid !== this.appId) {
      throw new Error(`App ID mismatch: expected ${this.appId}, got ${decoded.watermark.appid}`)
    }

    return decoded

  } catch (err) {
    console.error('Decryption error:', err.message)
    console.error('Error stack:', err.stack)
    throw new Error(`Decryption failed: ${err.message}`)
  }
}

export default WXBizDataCrypt
