<template>
  <div class="app">
  </div>
</template>

<script setup>
// var WXBizDataCrypt = require('../utils/index')
import WXBizDataCrypt from '../utils/index'
console.log('WXBizDataCrypt', WXBizDataCrypt)

var appId = 'wxa4a449ac8f266f01'
var sessionKey = '9ZiH10bMcnQAjildz99Eqg=='
var encryptedData = 
	'h5C600A3HzoEbjcYCuhPwhS7sXoi4ZHH18yfu6+PHL7v59wH0zEmEvFH2NfQS2jKWwdGhlFCT9AQ10f3FrfbmwbbG/McZgVKW9s+5LV6vJ0='
var iv = 'gCDxCS8VSJnhgOl5Iyz9MA=='

var pc = new WXBizDataCrypt(appId, sessionKey)

try {
  var data = pc.decryptData(encryptedData, iv)
  console.log('解密后 data: ', data)
} catch (error) {
  console.error('解密失败:', error.message)
  console.error('完整错误:', error)
}
// 解密后的数据为
//
// data = {
//   "nickName": "Band",
//   "gender": 1,
//   "language": "zh_CN",
//   "city": "Guangzhou",
//   "province": "Guangdong",
//   "country": "CN",
//   "avatarUrl": "http://wx.qlogo.cn/mmopen/vi_32/aSKcBBPpibyKNicHNTMM0qJVh8Kjgiak2AHWr8MHM4WgMEm7GFhsf8OYrySdbvAMvTsw3mo8ibKicsnfN5pRjl1p8HQ/0",
//   "unionId": "ocMvos6NjeKLIBqg5Mr9QjxrP1FA",
//   "watermark": {
//     "timestamp": 1477314187,
//     "appid": "wx4f4bc4dec97d474b"
//   }
// }

</script>

<style>
</style> 